import { PostRequestWithHeaders } from "./api"
export default function EndSessionApi(roomId) {
    PostRequestWithHeaders({
        url: process.env.REACT_APP_API_BACKEND_API_URL + 'session/EndSession', body: {
            "session_id": roomId,
        }
    }).then(response => {

    })

}

export async function ExtendSessionApi(sessionId, duration) {
    try {
        const response = await PostRequestWithHeaders({
            url: process.env.REACT_APP_API_BACKEND_API_URL +`session/ExtendSession`,
            body: {
                session_id: sessionId,
                duration: duration
            }
        });
        // console.log("Extend session response:", response);
        return response;
    } catch (error) {
        console.error("Error in ExtendSessionApi:", error);
        throw error;
    }
}

export async function CheckAvailabilityApi(config) {
    try {
        const response = await PostRequestWithHeaders({
            url: process.env.REACT_APP_API_BACKEND_API_URL +`session/checkAvailability`,
            body: config
        });
        console.log("Check availability response:", response);
        return response;
    } catch (error) {
        console.error("Error in CheckAvailabilityApi:", error);
        throw error;
    }
}


export async function UpdateSessionApi(sessionId, projectId, type, isPixelstreamingActive = false, durationMinutes, leadId = null) {
    try {
        // Validate session type
        const validTypes = ['default', 'pixel_streaming', 'ale', 'lark'];
        if (!validTypes.includes(type)) {
            console.warn(`Invalid session type: ${type}. Using 'default' instead.`);
            type = 'default';
        }

        // Determine if pixel streaming is active based on type
        const isPixelActive = isPixelstreamingActive || type === 'pixel_streaming' || type === 'lark';

        // Build the request body
        const body = {
            "session_id": sessionId,
            "project_id": projectId,
            "type": type,
            "is_pixelstreaming_active": isPixelActive,
            "duration_minutes": durationMinutes
        };

        // Add optional parameters if provided
        if (durationMinutes !== null) {
            body.duration_minutes = durationMinutes;
        }

        if (leadId !== null) {
            body.lead_id = leadId;
        }

        // Make the API call
        const response = await PostRequestWithHeaders({
            url: process.env.REACT_APP_API_BACKEND_API_URL + 'session/UpdateSession',
            body: body
        });

        console.log("Update session response:", response);
        return response;
    } catch (error) {
        console.error("Error in UpdateSessionApi:", error);
        throw error;
    }
}