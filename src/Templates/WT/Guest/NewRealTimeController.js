import React, { useEffect, useState } from "react";
import { connect } from "react-redux";
import * as HostActions from "../../../Actions/HostAction";
import * as ExceptionActions from "../../../Actions/Exception";
import { getScreenSize, addScreenResizeListener } from '../../../utils/screenUtils';
import { socket } from "../../../Actions/HostAction";
import CloseModal from "./CloseModal";
import MobileControls from "../../../components/NewRealTImeController/MobileScreen";
import TabletControls from "../../../components/NewRealTImeController/TabScreen";
import DesktopControls from "../../../components/NewRealTImeController/DesktopScreen";
import FullscreenModal from "../../../components/Modals/FullScreenModal";

const NewRealTimeController = (props) => {
    const [modals, setModals] = useState({
        close: false,
        fullscreenModal: true,
    });

    // State for screen size
    const [screenSize, setScreenSize] = useState(getScreenSize());
    const [hostCtrl, setHostCtrl] = useState(false);
    const [fullscreen, setFullscreen] = useState(false);

    // Set up screen size listener
    useEffect(() => {
        const removeListener = addScreenResizeListener((newSize) => {
            setScreenSize(newSize);
        });

        // Clean up listener on unmount
        return () => {
            if (removeListener) removeListener();
        };
    }, []);

    // Listen for host mute all command
    useEffect(() => {
        socket.on('GuestMuteAll', ({ status }) => {
            if (status) {
                if (props.Audio) {
                    handleAudioControl();
                }
                setHostCtrl(true);
            } else {
                if (!props.Audio) {
                    handleAudioControl();
                }
                setHostCtrl(false);
            }
        });

        return () => {
            socket.off('GuestMuteAll');
        };
    }, [props.Audio]);

    const toggleModal = (modalName, value) => {
        setModals(prev => ({
            ...prev,
            [modalName]: value
        }));
    };

    const handleVideoControl = () => {
        if (props.CameraAccess) {
            props.ToggleUserVideo({
                roomId: props.roomId,
                Peers: props.Peers,
                Audio: props.Audio,
                Video: props.Video,
                Screen: props.ScreenShare,
                LocalStream: props.LocalStream
            });
        } else {
            props.SetModalException("Camera is not Attached");
        }
    };

    const handleAudioControl = () => {
        if (!hostCtrl) {
            if (props.MicrophoneAccess) {
                props.ToggleUserAudio({
                    roomId: props.roomId,
                    Peers: props.Peers,
                    Audio: props.Audio,
                    Video: props.Video,
                    Screen: props.ScreenShare,
                    LocalStream: props.LocalStream
                });
            } else {
                props.SetModalException("Mic is not Attached");
            }
        } else {
            props.SetStickyException("Host has the control");
        }
    };
    const handleEndSessionWithModal = () => {
        setModals(prev => ({ ...prev, close: true }));
      };

    const handleTabControl = (tabName) => {
        if (tabName === "CHAT") {
            // Toggle chat sidebar
            props.ToggleChat();

            // Reset unread count when opening chat tab
            if (props.UnreadCount > 0) {
                props.SetUnreadMessage(0);
            }
        } else if (tabName === "MEMBERS") {
            // Toggle members sidebar
            props.ToggleMembers();
        }
    };

    const handleEndSession = () => {
        console.log("handleEndSession called");
        toggleModal('close', true);
    };

    const closeSession = () => {
        window.location = "/salestool/feedback/" + props.roomId + "/" + props.ClientData.GuestId;
    };

    const toggleFullscreen = () => {
        toggleModal('fullscreenModal', false);

        if (!fullscreen || window.innerHeight !== window.screen.height) {
            setFullscreen(true);
            let element = document.getElementById("MainContentDiv");
            if (element.requestFullscreen) {
                element.requestFullscreen();
            } else if (element.mozRequestFullScreen) {
                element.mozRequestFullScreen();
            } else if (element.webkitRequestFullscreen) {
                element.webkitRequestFullscreen();
            } else if (element.msRequestFullscreen) {
                element.msRequestFullscreen();
            }
        }
    };

    const handleScreenShare = () => {
        props.ToggleScreenVideo({
            roomId: props.roomId,
            Peers: props.Peers,
            Audio: props.Audio,
            Video: props.Video,
            Screen: props.ScreenShare,
            LocalStream: props.LocalStream
        });
    };

    // Floating controls are now handled in Main.js
    const renderFloatingControls = () => null;

    // Render fullscreen modal
    const renderFullscreenModal = () => (
        modals.fullscreenModal && (
            <FullscreenModal
                onAccept={toggleFullscreen}
                onDecline={() => toggleModal('fullscreenModal', false)}
            />
        )
    );

    // Render modals
    const renderModals = () => {
        // console.log("renderModals called, modals.close:", modals.close);
        return (
            <>
                {/* Force modal to always render for debugging */}
                {modals.close && (
                <CloseModal
                type="exit"
                    roomid={props.roomId}
                    CloseSession={closeSession}
                    Close={() => {
                    console.log("Closing end session modal");
                    setModals(prev => ({ ...prev, close: false }));
                }}
                />)}
            </>
        );
    };

    // Main render
    return (
        <>
            {renderFloatingControls()}

            {/* Desktop Controls */}
            {props.ShowControls && screenSize === 'desktop' && (
                <DesktopControls
                    Audio={props.Audio}
                    Video={props.Video}
                    Tab={props.Tab}
                    ChatOpen={props.ChatOpen}
                    MembersOpen={props.MembersOpen}
                    UnreadCount={props.UnreadCount}
                    userscount={props.userscount + 1}
                    handleAudioControl={handleAudioControl}
                    handleVideoControl={handleVideoControl}
                    handleTabControl={handleTabControl}
                    handleEndSessionWithModal={handleEndSessionWithModal}
                    handleEndSession={handleEndSession}
                    toggleFullscreen={toggleFullscreen}
                    handleInvertControls={(showControls) => props.INVERT_CONTROLS(showControls)}
                    ShowControls={props.ShowControls}
                    handleShareProject={null} // Guest doesn't have share project functionality
                    handleScreenShare={handleScreenShare}
                    ScreenShare={props.ScreenShare}
                    switchingProject={false} // Guest doesn't have project switching
                    SessionDetails={props.Config}
                    roomId={props.roomId}
                    isGuest={true} // Flag to indicate this is a guest controller
                />
            )}

            {/* Mobile Controls */}
            {props.ShowControls && screenSize === 'mobile' && (
                <div>
                    <MobileControls
                        Audio={props.Audio}
                        Video={props.Video}
                        Tab={props.Tab}
                        ChatOpen={props.ChatOpen}
                        MembersOpen={props.MembersOpen}
                        UnreadCount={props.UnreadCount}
                        userscount={props.userscount + 1}
                        handleAudioControl={handleAudioControl}
                        handleVideoControl={handleVideoControl}
                        handleTabControl={handleTabControl}
                        handleEndSession={handleEndSession}
                        handleEndSessionWithModal={handleEndSessionWithModal}
                        toggleFullscreen={toggleFullscreen}
                        handleInvertControls={() => props.TOGGLE_CONTROLS(false)}
                        ShowControls={props.ShowControls}
                        handleShareProject={null} // Guest doesn't have share project functionality
                        handleScreenShare={handleScreenShare}
                        ScreenShare={props.ScreenShare}
                        switchingProject={false} // Guest doesn't have project switching
                        SessionDetails={props.Config}
                        roomId={props.roomId}
                        isGuest={true} // Flag to indicate this is a guest controller
                    />
                </div>
            )}

            {/* Tablet Controls */}
            {props.ShowControls && screenSize === 'tablet' && (
                <div>
                    <TabletControls
                        Audio={props.Audio}
                        Video={props.Video}
                        Tab={props.Tab}
                        ChatOpen={props.ChatOpen}
                        MembersOpen={props.MembersOpen}
                        UnreadCount={props.UnreadCount}
                        userscount={props.userscount + 1}
                        handleAudioControl={handleAudioControl}
                        handleVideoControl={handleVideoControl}
                        handleTabControl={handleTabControl}
                        handleEndSession={handleEndSession}
                        toggleFullscreen={toggleFullscreen}
                        handleEndSessionWithModal={handleEndSessionWithModal}
                        handleInvertControls={() => props.TOGGLE_CONTROLS(false)}
                        ShowControls={props.ShowControls}
                        handleShareProject={null} // Guest doesn't have share project functionality
                        handleScreenShare={handleScreenShare}
                        ScreenShare={props.ScreenShare}
                        switchingProject={false} // Guest doesn't have project switching
                        SessionDetails={props.Config}
                        roomId={props.roomId}
                        isGuest={true} // Flag to indicate this is a guest controller
                    />
                </div>
            )}

            {renderFullscreenModal()}
            {renderModals()}
        </>
    );
};

const mapStateToProps = state => {
    return {
        Peers: state.Call.peers,
        SocketId: state.Call.SocketId,
        LocalStream: state.Call.LocalStream,
        UserName: state.Call.UserName,
        Video: state.Call.Video,
        Audio: state.Call.Audio,
        ScreenShare: state.Call.ScreenShare,
        CameraAccess: state.Call.HasCamera,
        MicrophoneAccess: state.Call.HasMicrophone,
        ClientData: state.Call.ClientData,
        Tab: state.Call.Tab,
        ChatOpen: state.Call.ChatOpen,
        MembersOpen: state.Call.MembersOpen,
        userscount: state.Call.userscount,
        UnreadCount: state.Call.UnreadCount,
        ShowControls: state.Call.ShowControls,
        Config: state.Call.config,
    };
};

const mapDispatchToProps = {
    ...HostActions,
    ...ExceptionActions
};

export default connect(mapStateToProps, mapDispatchToProps)(NewRealTimeController);