import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import Fire from "../../../config/Firebase.jsx";
import * as HostActions from "../../../Actions/HostAction";
import { connect } from "react-redux";
import { PostRequest } from "../../../Tools/helpers/api.js";
import { setCookie } from "../../../Tools/helpers/domhelper.js";
import banner from '../../../assets/img/guest_joining/Banner.png';
import * as Sessions from "../../../Actions/Sessions.js";
import * as ExceptionActions from "../../../Actions/Exception";
import VideoStream from "../../../components/VideoStream";

const NewJoinRoom = (props) => {
  const [name, setName] = useState("");
  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [noRoom, setNoRoom] = useState(false);
  const [loading, setLoading] = useState(true);
  const [isRejoining, setIsRejoining] = useState(false);
  const [leadId, setLeadId] = useState(null);
  const [guestId, setGuestId] = useState(null);
  const [leadDataFetched, setLeadDataFetched] = useState(false);
  const navigate = useNavigate();

  // Destructure essential props including LocalStream for video preview and session joining
  const { roomId, ClientData, SetClientData, SetConfig, LocalStream, SetLocalStream, DummyAudio, Join, SetProjectDetails, Video, Audio, ToogleLocalAV, SetUserAV } = props;

  useEffect(() => {
    // console.log("!!!", roomId, props);

    // Parse URL parameters for rejoining flow like old joinroom
    const params = new URL(document.location).searchParams;
    const urlLeadId = params.get("lead_id");
    const urlGuestId = params.get("user");

    setLeadId(urlLeadId);
    setGuestId(urlGuestId);

    // Handle rejoining flow if lead_id is present - just prefill name
    if (urlLeadId && !leadDataFetched) {
      // console.log("Rejoining user detected with lead_id:", urlLeadId, "- Making API call");
      setIsRejoining(true);
      setLeadDataFetched(true); // Prevent multiple API calls

      PostRequest({
        url: process.env.REACT_APP_API_BACKEND_API_URL + 'publicapis/GetLead',
        body: {
          "session_id": roomId,
          "lead_id": urlLeadId,
        }
      }).then(response => {
        // console.log("Lead data fetched for rejoining user:", response);

        // Store the lead data for when user clicks join
        const clientLeadData = {
          GuestId: response.key || response._id, // Use key or _id as GuestId
          data: response
        };
        localStorage.setItem(roomId, JSON.stringify(clientLeadData));
        SetClientData(clientLeadData);

        // Prefill the name from the existing lead data
        if (response.name) {
          setName(response.name);
          // console.log("Prefilled name for rejoining user:", response.name);
        }

        // Check if project data exists and store it for later use
        const leadData = response;
        if (leadData.organization_id &&
            leadData.interested_in &&
            leadData.interested_in.length > 0 &&
            leadData.interested_in[0].project_id) {

          // console.log("Fetching project details for project_id:", leadData.interested_in[0].project_id);
          return PostRequest({
            url: process.env.REACT_APP_API_BACKEND_API_URL + `publicapis/organization/${leadData.organization_id}/project/${leadData.interested_in[0].project_id}/getProject`
          });
        } else {
          // console.log("No project selected or project_id undefined, skipping project fetch");
          return Promise.resolve(null);
        }
      }).then(response => {
        if (response) {
          // console.log("Project data fetched for rejoining user:", response);
          setCookie("projectName", response.name);
          SetProjectDetails(response);
          localStorage.setItem('projectDetails', JSON.stringify(response));
        }

        // Show the form with prefilled name - user will click join as usual
        setIsRejoining(false);
        setLoading(false);
        // console.log("Rejoining user data loaded, showing form with prefilled name");
      }).catch(error => {
        // console.error("Error in rejoining flow:", error);
        // Fall back to normal flow if rejoining fails
        setIsRejoining(false);
        setLoading(false);
        setLeadDataFetched(false); // Reset flag to allow retry if needed
        // Remove URL parameters to show normal form
        // window.history.replaceState({}, document.title, window.location.pathname);
      });
    } else if (urlLeadId && leadDataFetched) {
      // console.log("Lead data already fetched for lead_id:", urlLeadId, "- Skipping API call");
    } else {
      // Normal flow for new users or users reloading the page
      // Check if client data already exists in localStorage (page reload scenario)
      if (!ClientData) {
        const clientData = localStorage.getItem(roomId);
        if (clientData) {
          const parsedData = JSON.parse(clientData);

          // Ensure GuestId is properly set
          const correctedData = {
            ...parsedData,
            GuestId: parsedData.GuestId || (parsedData.data && parsedData.data._id)
          };

          SetClientData(correctedData);
          // console.log("Found existing data in localStorage for returning user:", correctedData);

          // Prefill name from localStorage data if available
          if (correctedData.data && correctedData.data.name) {
            setName(correctedData.data.name);
            // console.log("Prefilled name from localStorage on page reload:", correctedData.data.name);
          }
        } else {
          // console.log("No existing data found in localStorage - new user");
        }
      } else if (ClientData && ClientData.data && ClientData.data.name && !name) {
        // If ClientData exists but name is not set, prefill it
        setName(ClientData.data.name);
        // console.log("Prefilled name from existing ClientData:", ClientData.data.name);
      }
    }

    // Check if the room exists (common for both flows)
    Fire.firestore().collection("sessions").doc(roomId).get().then((doc) => {
      if (doc.exists) {
        if (doc.data().status === "ended") {
          navigate(`/salestool/feedback/${roomId}`);
        }
        SetConfig(doc.data());
        // console.log("SetConfig from Firestore", doc.data());

        // Fetch lead name from roomdata.target.displayName like old join room (only for new users)
        if (!urlLeadId && doc.data().target && doc.data().target.displayName) {
          setName(doc.data().target.displayName);
        }
      } else {
        setNoRoom(true);
      }

      // Only set loading to false for new users, rejoining users handle their own loading state
      if (!urlLeadId) {
        setLoading(false);
      }
    }).catch(error => {
      // console.error("Error checking room:", error);
      setNoRoom(true);
      setLoading(false);
    });

    // Initialize LocalStream for video preview like JoiningPageHost
    if (!LocalStream) {
      SetLocalStream(DummyAudio);
    }

  }, [roomId, navigate, SetConfig, ClientData, SetClientData, LocalStream, SetLocalStream, DummyAudio, Join, SetProjectDetails, leadDataFetched]);

  const handleChange = (e) => {
    const { name, value } = e.target;

    if (name === "name") {
      setName(value);
      // Clear error when user types
      if (errors.name) {
        setErrors(prev => ({ ...prev, name: null }));
      }
    }
  };

  // Video toggle function similar to JoiningPageHost
  const handleVideo = () => {
    if (Video) {
      // Turn off video - store current track and use canvas
      const currentTrack = LocalStream.getVideoTracks()[0];
      if (currentTrack && currentTrack.label !== 'canvas') {
        window.realVideoTrack = currentTrack;
      }

      LocalStream.removeTrack(LocalStream.getVideoTracks()[0]);
      LocalStream.addTrack(props.CanvasTrack);
      ToogleLocalAV("Video", false);

      SetUserAV("localUser", {
        video: false,
        audio: Audio,
        screen: false
      });
    } else {
      // Turn on video - use stored track or get new one
      if (window.realVideoTrack) {
        LocalStream.removeTrack(LocalStream.getVideoTracks()[0]);
        LocalStream.addTrack(window.realVideoTrack);
        ToogleLocalAV("Video", true);

        SetUserAV("localUser", {
          video: true,
          audio: Audio,
          screen: false
        });
      } else {
        // Get new video track
        navigator.mediaDevices.getUserMedia({video: true}).then(stream => {
          LocalStream.removeTrack(LocalStream.getVideoTracks()[0]);
          LocalStream.addTrack(stream.getVideoTracks()[0]);
          ToogleLocalAV("Video", true);

          window.realVideoTrack = stream.getVideoTracks()[0];

          SetUserAV("localUser", {
            video: true,
            audio: Audio,
            screen: false
          });
        }).catch(err => {
          console.error("Error getting video track:", err);
        });
      }
    }
  };

  // Audio toggle function similar to JoiningPageHost
  const handleAudio = () => {
    if (Audio) {
      // Turn off audio
      LocalStream.getAudioTracks()[0].enabled = false;
      ToogleLocalAV("Audio", false);

      SetUserAV("localUser", {
        video: Video,
        audio: false,
        screen: false
      });
    } else {
      // Turn on audio
      LocalStream.getAudioTracks()[0].enabled = true;
      ToogleLocalAV("Audio", true);

      SetUserAV("localUser", {
        video: Video,
        audio: true,
        screen: false
      });
    }
  };

  const validateForm = () => {
    const newErrors = {};

    // Validate name
    if (!name.trim()) {
      newErrors.name = "Name is required";
    } else if (!/^[a-zA-Z-,]+(\s{0,1}[a-zA-Z-, ])*$/.test(name)) {
      newErrors.name = "Contains only alphabets [A-Z]";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      // console.log("Joining room with name:", ClientData, localStorage.getItem(roomId));

      // Check if this is a rejoining user with existing ClientData or localStorage data
      const localStorageData = JSON.parse(localStorage.getItem(roomId) || 'null');
      const existingData = ClientData || localStorageData;

      // console.log("Checking existing data - ClientData:", ClientData);
      // console.log("Checking existing data - localStorage:", localStorageData);
      // console.log("Final existingData:", existingData);

      // Check if we have existing data (either with GuestId or just data with _id)
      const hasExistingData = existingData && (
        (existingData.GuestId && existingData.data) ||
        (existingData.data && existingData.data._id)
      );

      if (hasExistingData) {
        // console.log("Rejoining user with existing data, skipping CreateLead API:", existingData);

        // Update the name in case user changed it and ensure GuestId is set
        const updatedData = {
          ...existingData,
          GuestId: existingData.GuestId || existingData.data._id, // Use _id if GuestId is missing
          data: {
            ...existingData.data,
            name: name
          }
        };

        // console.log("Updated data with proper GuestId:", updatedData);

        // Update localStorage and ClientData with the new name
        localStorage.setItem(roomId, JSON.stringify(updatedData));
        SetClientData(updatedData);

        // Wait a moment for ClientData to be set, then call Join()
        setTimeout(() => {
          // console.log("Calling Join() for rejoining user with data:", updatedData);
          Join();
        }, 100);
        return;
      }

      // For new users, create a new lead
      // console.log("New user, creating lead");
      const userEmail = `guest_${Date.now()}@example.com`;
      const userPhone = "";

      PostRequest({
        url: process.env.REACT_APP_API_BACKEND_API_URL + 'publicapis/CreateLead',
        body: {
          "name": name,
          "email": userEmail,
          "phone_number": userPhone,
          "session_id": roomId,
          source: 'sales_session',
          status: 'new',
        }
      }).then((response) => {
        const leadData = { GuestId: response.key, data: response };
        localStorage.setItem(roomId, JSON.stringify(leadData));
        SetClientData(leadData);

        // Wait a moment for ClientData to be set, then call Join()
        setTimeout(() => {
          // console.log("Calling Join() for new user with data:", leadData);
          Join();
        }, 100);
      }).catch(error => {
        // console.error("Error creating lead:", error);
        setIsSubmitting(false);
      });
    } catch (error) {
      // console.error("Error in form submission:", error);
      setIsSubmitting(false);
    }
  };



  // Show loading for initial room check or when fetching rejoining user data
  if (loading || isRejoining) {
    return (
      <div className="flex justify-center items-center h-screen bg-blue-600">
        <div className="w-12 h-12 border-4 border-white border-t-transparent rounded-full animate-spin"></div>
        {isRejoining && (
          <div className="ml-4 text-white text-sm">
            Loading your information...
          </div>
        )}
      </div>
    );
  }

  if (noRoom) {
    return (
      <div className="flex justify-center items-center h-screen bg-blue-600">
        <div className="bg-white p-6 rounded-lg shadow-lg">
          <h5 className="text-xl font-bold mb-2">Session not Found</h5>
          <p className="mb-4">Kindly re-check the invite link</p>
          <button
            onClick={() => window.history.back()}
            className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
          >
            Go Back
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen w-full bg-blue-600 flex flex-col items-center justify-center relative overflow-hidden">
      {/* Background pattern */}
      <div className="absolute inset-0 bg-blue-600 opacity-80 z-0">
        <img
          src={banner}
          alt="Background"
          className="w-full h-full object-cover opacity-20"
        />
      </div>

      {/* Content container */}
      <div className="z-10 w-full max-w-md px-4">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-white text-[20px] lg:text-[28px] font-normal mb-3">Property Experience Meeting</h1>
          {ClientData && ClientData.GuestId && (
            <p className="text-white/80 text-sm mb-2">Welcome back!</p>
          )}
          <div className="w-48 mx-auto">
            <div className="border-b border-white/30 pb-1">
              <input
                type="text"
                name="name"
                value={name}
                onChange={handleChange}
                placeholder="Enter Name"
                autoComplete="off"
                className="bg-transparent text-white text-start w-full outline-none autofill:bg-transparent"
              />

            </div>
            {errors.name && (
              <p className="text-red-200 text-xs mt-1">{errors.name}</p>
            )}
          </div>
        </div>

        {/* Video preview like JoiningPageHost */}
        <div className="relative mb-4">
          <div className="rounded-lg w-[90%] mx-auto overflow-hidden bg-white/10 backdrop-blur-sm">
            <div className="aspect-video relative">
              <div className="w-full h-full rounded-xl bg-black">
                <div className="h-fit w-full">
                  {LocalStream ? <VideoStream /> : (
                    <div className="w-full h-full bg-gray-800 flex items-center justify-center">
                      {/* <div className="w-20 h-20 rounded-full bg-blue-500 flex items-center justify-center text-white text-2xl font-bold">
                        {name ? name.charAt(0).toUpperCase() : ""}
                      </div> */}
                    </div>
                  )}
                </div>
              </div>

              {/* Video/Audio Controls */}
              <div className="absolute bottom-3 left-1/2 transform -translate-x-1/2 flex gap-3">
                {/* Video Toggle Button */}
                <button
                  onClick={handleVideo}
                  className={`w-10 h-10 rounded-full flex items-center justify-center transition-colors ${
                    Video ? 'bg-white text-black' : 'bg-red-500 text-white'
                  }`}
                  title={Video ? "Turn off camera" : "Turn on camera"}
                >
                  {Video ? (
                    <svg className="w-5 h-5" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M17,10.5V7A1,1 0 0,0 16,6H4A1,1 0 0,0 3,7V17A1,1 0 0,0 4,18H16A1,1 0 0,0 17,17V13.5L21,17.5V6.5L17,10.5Z" />
                    </svg>
                  ) : (
                    <svg className="w-5 h-5" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M22,16.5L20.5,15L19,16.5L17.5,15L16,16.5L17.5,18L16,19.5L17.5,21L19,19.5L20.5,21L22,19.5L20.5,18L22,16.5M16,4A1,1 0 0,1 17,5V8.5L21,4.5V11.8C20.7,11.9 20.4,12.1 20.1,12.3L17,9.2V12A1,1 0 0,1 16,13H7.9L16,4M3.4,1.7L1.7,3.4L5.9,7.6C5.8,7.7 5.8,7.8 5.8,8V16A1,1 0 0,0 6.8,17H15A1,1 0 0,0 16,16V15.9L18.6,18.5L20.3,16.8L3.4,1.7Z" />
                    </svg>
                  )}
                </button>

                {/* Audio Toggle Button */}
                <button
                  onClick={handleAudio}
                  className={`w-10 h-10 rounded-full flex items-center justify-center transition-colors ${
                    Audio ? 'bg-white text-black' : 'bg-red-500 text-white'
                  }`}
                  title={Audio ? "Turn off microphone" : "Turn on microphone"}
                >
                  {Audio ? (
                    <svg className="w-5 h-5" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M12,2A3,3 0 0,1 15,5V11A3,3 0 0,1 12,14A3,3 0 0,1 9,11V5A3,3 0 0,1 12,2M19,11C19,14.53 16.39,17.44 13,17.93V21H11V17.93C7.61,17.44 5,14.53 5,11H7A5,5 0 0,0 12,16A5,5 0 0,0 17,11H19Z" />
                    </svg>
                  ) : (
                    <svg className="w-5 h-5" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M19,11C19,12.19 18.66,13.3 18.1,14.28L16.87,13.05C17.14,12.43 17.3,11.74 17.3,11H19M15,11.16L9,5.18V5A3,3 0 0,1 12,2A3,3 0 0,1 15,5V11L15,11.16M4.27,3L21,19.73L19.73,21L15.54,16.81C14.77,17.27 13.91,17.58 13,17.72V21H11V17.72C7.72,17.23 5,14.41 5,11H7A5,5 0 0,0 12,16C12.81,16 13.6,15.78 14.27,15.38L12.8,13.91C12.55,13.95 12.28,14 12,14A3,3 0 0,1 9,11V10.18L4.27,3Z" />
                    </svg>
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Join button */}
        <div className="mb-4">
          <button
            onClick={handleSubmit}
            disabled={isSubmitting}
            className="w-[80%] mx-auto bg-white text-[14px] gap-2 font-semibold py-2 px-4 rounded-md hover:bg-blue-50 transition-colors flex items-center justify-center"
          >
            {isSubmitting ? (
              <i className="fa fa-circle-o-notch fa-spin mr-2"></i>
            ) : (
              <svg width="14" height="15" viewBox="0 0 14 15" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M7.69999 2.25H1.4C0.626801 2.25 0 2.92157 0 3.75V11.25C0 12.0784 0.626801 12.75 1.4 12.75H7.69999C8.47319 12.75 9.09999 12.0784 9.09999 11.25V3.75C9.09999 2.92157 8.47319 2.25 7.69999 2.25Z" fill="black"/>
<path d="M13.65 3.225C13.5439 3.15849 13.4234 3.12306 13.3006 3.12227C13.1777 3.12148 13.0568 3.15536 12.95 3.2205L10.5 4.713V10.3627L12.9255 12.0128C13.0316 12.0848 13.1539 12.1248 13.2794 12.1288C13.405 12.1327 13.5292 12.1004 13.6391 12.0352C13.749 11.97 13.8405 11.8744 13.904 11.7582C13.9675 11.6421 14.0006 11.5098 14 11.3752V3.87525C14.0001 3.74347 13.9678 3.61399 13.9064 3.49984C13.8449 3.38568 13.7565 3.29089 13.65 3.225Z" fill="black"/>
</svg>

            )}
            Join Meeting
          </button>
        </div>

        {/* Status message */}
        {/* <div className="text-center">
          <p className="text-white/80 text-sm">No one is in the call yet</p>
        </div> */}
      </div>
    </div>
  );
};

const mapStateToProps = (state) => {
  return {
    Config: state.Call.config,
    ProjectDetails: state.Sessions.projectDetails,
    SETUP: state.Call.SETUP,
    CanvasTrack: state.Call.CanvasTrack,
    ModalException: state.Exception.modalexception,
    DummyAudio: state.Call.DummyAudio,
    ClientData: state.Call.ClientData,
    SessionDetails: state.Sessions.sessions,
    LocalStream: state.Call.LocalStream,
    NetWorkSpeed: state.Call.NetWorkSpeed,
    Video: state.Call.Video,
    Audio: state.Call.Audio,
  };
};

const mapDispatchToProps = {
  ...HostActions,
  ...Sessions,
  ...ExceptionActions,
};

export default connect(mapStateToProps, mapDispatchToProps)(NewJoinRoom);
