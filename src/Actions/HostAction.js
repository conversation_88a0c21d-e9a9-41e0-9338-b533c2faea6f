import io from "socket.io-client"
import Peer from 'simple-peer';
import { Firebase } from "../config/Firebase";
export var socket;
export const peersRef=[];
var AudioContext ;
export var MUX ;
export var MUXDEST;
var c = document.createElement("canvas");
  c.width = 1600;  // 16
  c.height = 900;  // 9
var ctx = c.getContext("2d");
ctx.beginPath();
ctx.rect(20, 20, 150, 100);
// ctx.fillStyle = "red";
ctx.fill();
ctx.beginPath();
ctx.rect(40, 40, 150, 100);
ctx.fillStyle = "black";
ctx.fill();
const canvasstreamg = c.captureStream(1)
export const canvastrack = canvasstreamg.getVideoTracks()[0]
canvastrack.enabled = false;
var screenTrackRef;
var currentVideoTrack; // Store the current video track when screen sharing starts
var localDisplayStream; // Separate stream for local video display when screen sharing is active

// Helper function to safely replace video tracks
function replaceVideoTrack(peers, localStream, newTrack) {
  try {
    console.log('replaceVideoTrack called with:', {
      peersCount: Object.keys(peers).length,
      newTrack: newTrack,
      trackKind: newTrack.kind,
      trackEnabled: newTrack.enabled,
      trackReadyState: newTrack.readyState
    });

    // Replace track for all peers
    Object.values(peers).forEach((peer, index) => {
      if (peer && peer.streams && peer.streams[0]) {
        const oldTrack = peer.streams[0].getTracks().find((track) => track.kind === 'video');
        if (oldTrack) {
          console.log(`Replacing track for peer ${index}:`, {
            oldTrack: oldTrack,
            newTrack: newTrack
          });
          peer.replaceTrack(oldTrack, newTrack, localStream);
        }
      }
    });

    // Replace track in local stream
    const currentVideoTrack = localStream.getVideoTracks()[0];
    if (currentVideoTrack) {
      console.log('Removing old track from LocalStream:', currentVideoTrack);
      localStream.removeTrack(currentVideoTrack);
    }
    console.log('Adding new track to LocalStream:', newTrack);
    localStream.addTrack(newTrack);
  } catch (error) {
    console.error('Error replacing video track:', error);
  }
}

// Helper function to create local display stream for video when screen sharing is active
function createLocalDisplayStream(videoTrack) {
  try {
    if (localDisplayStream) {
      // Clean up existing stream
      localDisplayStream.getTracks().forEach(track => track.stop());
    }
    localDisplayStream = new MediaStream([videoTrack]);

    // Update local video elements
    updateLocalVideoElements(localDisplayStream);

    return localDisplayStream;
  } catch (error) {
    console.error('Error creating local display stream:', error);
    return null;
  }
}

// Helper function to update local video elements
function updateLocalVideoElements(stream) {
  try {
    const localVideo = document.getElementById("LocalVideo");
    const localVideoMobile = document.getElementById("LocalVideoMobile");
    const typeSwitchLocalVideo = document.getElementById("TypeSwitchLocalVideo");

    if (localVideo) {
      localVideo.srcObject = stream;
    }
    if (localVideoMobile) {
      localVideoMobile.srcObject = stream;
    }
    if (typeSwitchLocalVideo) {
      typeSwitchLocalVideo.srcObject = stream;
    }
  } catch (error) {
    console.error('Error updating local video elements:', error);
  }
}

// Helper function to clean up screen sharing references
function cleanupScreenSharing() {
  console.log('Cleaning up screen sharing references');

  if (screenTrackRef) {
    try {
      console.log('Stopping screen track:', screenTrackRef.readyState);
      if (screenTrackRef.readyState === 'live') {
        screenTrackRef.stop();
      }
    } catch (error) {
      console.error('Error stopping screen track:', error);
    }
  }

  if (localDisplayStream) {
    try {
      console.log('Stopping local display stream tracks');
      localDisplayStream.getTracks().forEach(track => {
        if (track.readyState === 'live') {
          track.stop();
        }
      });
    } catch (error) {
      console.error('Error stopping local display stream:', error);
    }
  }

  if (currentVideoTrack) {
    try {
      console.log('Stopping current video track:', currentVideoTrack.readyState);
      if (currentVideoTrack.readyState === 'live') {
        currentVideoTrack.stop();
      }
    } catch (error) {
      console.error('Error stopping current video track:', error);
    }
  }

  // Clear all references
  screenTrackRef = null;
  currentVideoTrack = null;
  localDisplayStream = null;

  console.log('Screen sharing cleanup completed');
}
const IceServers = { iceServers: [{
  'urls': [
    'stun:stun.l.google.com:19302',
    'stun:stun1.l.google.com:19302',
    'stun:stun2.l.google.com:19302',
    'stun:stun3.l.google.com:19302',
    'stun:stun4.l.google.com:19302',
  ]
},
{
  "urls":"turn:34.18.30.39:19303",
  "username":"propvr",
  "credential":"revsmart@123"
},
{
  "urls":"turn:20.197.40.183:19303",
  "username":"propvr",
  "credential":"revsmart@123"
},

] }
export function ADDPEER(peer,config) {
    return dispatch => {
        dispatch({ type: "ADD_PEER", peer: peer })
    }
}
export function UpdateConfig(peer,config){
  return dispatch=>{
    dispatch({type:"UPDATE_CONFIG",peer:peer,config:config})
  }
}
export function INVERT_CONTROLS(controls){
  console.log('INVERT_CONTROLS action called with:', controls);
  return dispatch=>{
    dispatch({type:"INVERT_CONTROLS"})
    console.log('INVERT_CONTROLS action dispatched');
  }
}

export function TOGGLE_CONTROLS(value){
  console.log('TOGGLE_CONTROLS action called with:', value);
  return dispatch=>{
    dispatch({type:"TOGGLE_CONTROLS", value: value})
    console.log('TOGGLE_CONTROLS action dispatched with value:', value);
  }
}
export function UpdatePeer(peers, userid) {
    return dispatch => {
        dispatch({ type: "UPDATE_PEERS", peerid: userid })
    }
}
export function HasCamera(value){
  return dispatch=>{
    dispatch({type:"SET_HAS_CAMERA",value:value})
  }
}
export function SetConfig(config){
  return dispatch=>{
    dispatch({type:"SET_CONFIG",config:config})
  }
}
export function HasMicrophone(value){
  return dispatch=>{
    dispatch({type:"SET_HAS_MICROPHONE",value:value})
  }
}
export function SetVideoDevices() {
    return dispatch => {
        navigator.mediaDevices.enumerateDevices().then((devices) => {
            const filtered = devices.filter((device) => device.kind === 'videoinput');
            dispatch({ type: "SET_VIDEO_DEVICES", Devices: filtered })
        });
    }
}

export function SetHostStatus(status){
  return dispatch=>{
    dispatch({type:"SET_HOST_STATUS",status:status})
  }
}
export function SetRecorinngsQuota(UID){
  return dispatch=>{
  Firebase.storage().ref().child(UID + "/Salestool/Recordings").listAll().then(Records => {
    if (Records.prefixes.length) {
      Promise.all([...Records.prefixes].map(getRooms)).then(final => {
        dispatch({type:"SET_RECORD_QUOTA",size:final.reduce(function (a, b) { return a + b; }, 0)})
      })
    } else {
      dispatch({type:"SET_RECORD_QUOTA",size:0})
    }
  })


  }
}
export function ConnecToWebSocket(RoomId,Username){
    return dispatch=>{
      socket=io("https://propvr-socket.propvr.tech");
      socket.io.on("error", (error) => {
        // ...
        console.log(error)
      });
        const roomName = RoomId;
        const userName = Username;
        socket.on('FE-error-user-exist', ({ error }) => {
            if (!error) {
              sessionStorage.setItem('user', userName);
              dispatch({type:"CONNECTED_GOOD_TO_GO",socketid:socket.id,userName:Username})
            } else {
              dispatch({type:"CANT_CONNECT",payload:"User name already exist"})
            }
          });
    if (!roomName || !userName) {
      dispatch({type:"ENTER_VALID_FEILDS"})
    } else {
      socket.emit('BE-check-user', { roomId: roomName, userName });
    }
    }
}

export function SetLocalStream(DummyAudio){
    return dispatch=>{

      console.log('@@ SetLocalStream called - initializing with default OFF state');

      GetUserVideoMedia(true).then(Videotrack => {
        let RenderStream;
        if (Videotrack.stream) {
          // Initialize with video off by default
          console.log('@@ Video track available, initializing with video OFF (canvas track)');
          dispatch({type:"TOGGLE_LOCAL_AV",MediaType:"Video",value:false})
          dispatch({type:"SET_HAS_CAMERA",value:true})

          // Create a stream with the canvas track (blank video)
          RenderStream = new MediaStream([canvastrack]);
        } else {
          if ((Videotrack.error.toString()).includes("denied")) {
            dispatch({type:"SET_MODAL_EXCEPTION",message:"Camera Access is not allowed"})
          } else if ((Videotrack.error.toString()).includes("device")) {
            dispatch({type:"SET_MODAL_EXCEPTION",message:"Can't find any Camera attached to browser"})
          }
          console.log('@@ No video track available, using canvas track');
          dispatch({type:"SET_HAS_CAMERA",value:false})
          dispatch({type:"TOGGLE_LOCAL_AV",MediaType:"Video",value:false})

          // Use canvas track for video
          RenderStream = new MediaStream([canvastrack]);
        }
        return GetUserAudioMedia({ echoCancellation: true }).then(AudioTrack => {
          if (AudioTrack.stream && AudioTrack.track) {
            dispatch({type:"SET_HAS_MICROPHONE",value:true})

            // Initialize with audio off by default
            console.log('@@ Audio track available, initializing with audio OFF (disabled)');
            dispatch({type:"TOGGLE_LOCAL_AV",MediaType:"Audio",value:false})

            // Add the audio track but disable it
            RenderStream.addTrack(AudioTrack.track);
            RenderStream.getAudioTracks()[0].enabled = false;

            return RenderStream;
          } else {
            if ((AudioTrack.error.toString()).includes("denied")) {
              dispatch({type:"SET_MODAL_EXCEPTION",message:"Mic Access is not allowed"})
            } else if ((AudioTrack.error.toString()).includes("device")) {
              dispatch({type:"SET_MODAL_EXCEPTION",message:"Can't find any Mic attached to browser"})
            }
            console.log('@@ No audio track available, using dummy audio');
            dispatch({type:"SET_HAS_MICROPHONE",value:false})

            RenderStream.addTrack(DummyAudio)
            dispatch({type:"TOGGLE_LOCAL_AV",MediaType:"Audio",value:false})

            return RenderStream
          }
        })
      }).then(FinalStream=>{
        console.log('@@ Final stream created with initial state:', {
          videoTrackEnabled: FinalStream.getVideoTracks()[0]?.enabled,
          audioTrackEnabled: FinalStream.getAudioTracks()[0]?.enabled,
          videoTrackKind: FinalStream.getVideoTracks()[0]?.kind,
          audioTrackKind: FinalStream.getAudioTracks()[0]?.kind
        });
        
        AudioContext=  window.AudioContext // Default
    || window.webkitAudioContext // Safari and old versions of Chrome
    || false;
    MUX = new AudioContext();
    MUXDEST=MUX.createMediaStreamDestination()
      var _tempStream= new MediaStream([FinalStream.getAudioTracks()[0]])
      MUX.createMediaStreamSource(_tempStream).connect(MUXDEST);
        dispatch({type:"ADD_LOCAL_STREAM",LocalStream:FinalStream})

      })


    }
}
export function ConnectMUX(stream){
  return dispatch=>{
    dispatch({type:"MUX"})
    var _tempStreamremote= new MediaStream([stream.getAudioTracks()[0]])
    MUX.createMediaStreamSource(_tempStreamremote).connect(MUXDEST);
  }

}
export function MuteAll(roomId,status){
  return dispatch=>{
    socket.emit('HostMuteAll', { roomId:roomId, status:status });
    dispatch({type:""});
  }
}
export function Mute(id,roomId,status){
  return dispatch=>{
    socket.emit('HostMute', { id:id,roomId:roomId, status:status });
    dispatch({type:""});
  }
}
export function CheckInternetSpeed(){
  return dispatch=>{
    const SetNetwork=(network=>{
      if(network.downlink<= 1){
                    var time = new Date().getTime();

                    dispatch({type:"NETWORK_SPEED",payload:{speed:network.downlink,strength:"POOR"}})
                    // dispatch({type:"SET_TOAST_TOAST",toast:{[time]:{message:"Your network connectivity is unstable",type:"info",postmessage:""}}})
                    setTimeout(() => {
                      dispatch({type:"DELETE_TOAST",toastkey:time})
                    }, 5000);
                  }
                  else if(network.downlink>1 && network.downlink<=3){

                  // dispatch({type:"SET_TOAST_TOAST",toast:{[time]:{message:"Your network connectivity is unstable",type:"info",postmessage:""}}})
                  setTimeout(() => {
                    dispatch({type:"DELETE_TOAST",toastkey:time})
                  }, 5000);

                    dispatch({type:"NETWORK_SPEED",payload:{speed:network.downlink,strength:"MODERATE"}})

                  }else if(network.downlink>3 && network.downlink<=10){
                    dispatch({type:"NETWORK_SPEED",payload:{speed:network.downlink,strength:"GOOD"}})

                  }else if(network.downlink>=10){
                    dispatch({type:"NETWORK_SPEED",payload:{speed:network.downlink,strength:"GOOD"}})

                  }
    })
  if(navigator.connection!=undefined){
    const connection=navigator.connection;
    SetNetwork(navigator.connection)

    setInterval(() => {
    SetNetwork(navigator.connection)
    }, 90000);
    connection.onchange=(network=>{
     SetNetwork(network.target);
    })
  }
  else{
    testConnectionSpeed.run(1.5, (mbps)=>{
SetNetwork({downlink:mbps})
    }, (mbps)=>{
    } )

  }

  }
}
export function StartRecording(){
  return dispatch=>{

    dispatch({type:"START_RECORDING"})



  }
}
export function StopRecord(){
  return dispatch=>{
    dispatch({type:"STOP_RECORD"})

  }
}

export function SetUserAV(userName,config){
  return (dispatch, getState) => {
    console.log('@@ SetUserAV called:', {
      userName,
      config,
      currentUserVideoAudio: getState().Call.userVideoAudio
    });
    dispatch({type:"SET_AV_CONFIG",userName:userName,config:config})

    // Log state after dispatch
    setTimeout(() => {
      console.log('@@ SetUserAV completed - new state:', {
        userName,
        newUserVideoAudio: getState().Call.userVideoAudio,
        specificUserConfig: getState().Call.userVideoAudio[userName]
      });
    }, 10);
  }
}
export function ToogleLocalAV(type,value){
  return dispatch=>{
        dispatch({type:"TOGGLE_LOCAL_AV",MediaType:type,value:value})
  }
}
export function SetMessage(message){
  return dispatch=>{
    dispatch({type:"SET_MESSAGE",data:message})
  }
}
export function Call(stream,DataChannelMessage){
  return  (dispatch, getState)=>{
    // setInterval(() => {
    //   document.getElementById("LocalVideo").srcObject = stream;
    // }, 5000);

    socket.on('FE-user-join', (users) => {
      console.log('@@ FE-user-join received:', users.map(u => ({
        userId: u.userId,
        userName: u.info.userName,
        video: u.info.video,
        audio: u.info.audio,
        screen: u.info.screen
      })));

      // Get current Redux state to check existing userVideoAudio
      const currentState = getState();
      console.log('@@ Current userVideoAudio state before processing new users:', currentState.Call.userVideoAudio);
      console.log('@@ Current local user AV state:', {
        video: currentState.Call.Video,
        audio: currentState.Call.Audio,
        screen: currentState.Call.ScreenShare
      });

      // Process users sequentially with delays to ensure proper state synchronization
      users.forEach(({ userId, info }, index) => {

        let { userName, video, audio,extra,screen } = info;
        console.log('@@ Processing user join:', {
          userId,
          userName,
          video,
          audio,
          screen,
          isLocalUser: userId === socket.id,
          peerExists: !!findPeer(userId),
          currentUserVideoAudioForThisUser: currentState.Call.userVideoAudio[userId]
        });

        if (userId !== socket.id && !findPeer(userId )) {
          console.log('@@ Creating new peer for userId:', userId);
          console.log('@@ Current existing peers before adding new peer:', peersRef.map(p => ({
            peerID: p.peerID,
            userName: p.userName,
            currentVideoState: p.peer?.streams?.[0]?.getVideoTracks()?.[0]?.enabled,
            currentAudioState: p.peer?.streams?.[0]?.getAudioTracks()?.[0]?.enabled
          })));

          // Add a small delay for each peer to ensure sequential loading
          setTimeout(() => {
            const peer = createPeer(userId, socket.id, stream);
            peer.userName = userName;
            peer.peerID = userId;
            peer.extra=extra
            peer.Isdestroyed=false;
            peersRef.push({
              peerID: userId,
              peer,
              userName,
              extra
            });
            
            console.log('@@ Peer created and added to peersRef:', {
              userId,
              userName,
              initialVideo: video,
              initialAudio: audio,
              initialScreen: screen
            });

            console.log('@@ New peer will see existing participants with these states:', peersRef.filter(p => p.peerID !== userId).map(p => ({
              peerID: p.peerID,
              userName: p.userName,
              streamVideoEnabled: p.peer?.streams?.[0]?.getVideoTracks()?.[0]?.enabled,
              streamAudioEnabled: p.peer?.streams?.[0]?.getAudioTracks()?.[0]?.enabled
            })));

            peer.on('data', data => {
              let string = new TextDecoder().decode(data);
                      let Data=JSON.parse(string)
                      if(Data.type==="TRANSCRIPTION"){
                        this.props.AddLocalTranscription(Data)

                      }

                      if(Data.type==="GUEST_LOOK_AT_TO_HOST"){
                        if(document.getElementById(peer.peerID+"_VIDEOSPHERE"))
                        document.getElementById(peer.peerID+"_VIDEOSPHERE").setAttribute("position",Data.position)
                        // document.getElementById(peer.peerID+"_VIDEOSPHERE").setAttribute("animation","property: position; to: "+Data.position+"; loop: false; dur: 200")

                      }



              })

            // First add the peer to the state
            console.log('@@ Dispatching ADD_PEER for userId:', userId);
            dispatch({ type: "ADD_PEER", peer: peer });

            // Then set the AV config with a small delay to ensure peer is properly added
            setTimeout(() => {
              console.log('@@ Setting AV config for userId:', userId, {
                video,
                audio,
                screen
              });

              // Check state before setting AV config
              const stateBeforeAVConfig = getState();
              console.log('@@ State before SET_AV_CONFIG dispatch:', {
                userId,
                currentUserVideoAudio: stateBeforeAVConfig.Call.userVideoAudio,
                aboutToSetConfig: { video, audio, screen }
              });

              dispatch({type:"SET_AV_CONFIG",userName:userId,config:{ video, audio,screen }})

              // Check state immediately after setting AV config (reduced logging)
              setTimeout(() => {
                const stateAfterAVConfig = getState();
                if (!stateAfterAVConfig.Call.userVideoAudio[userId]) {
                  console.log('@@ ERROR: State after SET_AV_CONFIG dispatch - config missing for userId:', userId);
                }
              }, 10);

              // Additional delay to ensure state is fully synchronized for new users
              setTimeout(() => {
                console.log('@@ Peer state synchronized for:', userId);
                console.log('@@ Final state check - all peers after synchronization:', peersRef.map(p => ({
                  peerID: p.peerID,
                  userName: p.userName,
                  streamVideoEnabled: p.peer?.streams?.[0]?.getVideoTracks()?.[0]?.enabled,
                  streamAudioEnabled: p.peer?.streams?.[0]?.getAudioTracks()?.[0]?.enabled
                })));

                // Final Redux state check
                const finalState = getState();
                console.log('@@ Final Redux userVideoAudio state:', finalState.Call.userVideoAudio);
              }, 200);
            }, 100);

            let index = 5;
            setInterval(() => {
              if(index){
                const localVideo = document.getElementById("LocalVideo");
                if (localVideo) {
                  localVideo.srcObject = stream;
                }
                index--;
              }
            }, 5000);
          }, index * 200); // 200ms delay between each peer
        }
      });

    });
    socket.on('FE-receive-call', ({ signal, from, info }) => {
      let { userName, video, audio,extra,screen } = info;
      const peerIdx = findPeer(from);
      
      console.log('@@ FE-receive-call received:', {
        from,
        userName,
        video,
        audio,
        screen,
        peerExists: !!peerIdx
      });

      if (!peerIdx) {
        console.log('@@ Creating peer via addPeer for userId:', from);
        console.log('@@ Current existing peers before adding new peer (via receive-call):', peersRef.map(p => ({
          peerID: p.peerID,
          userName: p.userName,
          currentVideoState: p.peer?.streams?.[0]?.getVideoTracks()?.[0]?.enabled,
          currentAudioState: p.peer?.streams?.[0]?.getAudioTracks()?.[0]?.enabled
        })));

        const peer = addPeer(signal, from, stream);
        peer.userName = userName;
        peer.peerID = from;
        peer.extra=extra
        peersRef.push({
          peerID: from,
          peer,
          userName: userName,
          extra
        });
        
        console.log('@@ Peer created via addPeer and added to peersRef:', {
          userId: from,
          userName,
          initialVideo: video,
          initialAudio: audio,
          initialScreen: screen
        });

        console.log('@@ New peer (via receive-call) will see existing participants with these states:', peersRef.filter(p => p.peerID !== from).map(p => ({
          peerID: p.peerID,
          userName: p.userName,
          streamVideoEnabled: p.peer?.streams?.[0]?.getVideoTracks()?.[0]?.enabled,
          streamAudioEnabled: p.peer?.streams?.[0]?.getAudioTracks()?.[0]?.enabled
        })));
        
        peer.on('data', data => {
          let string = new TextDecoder().decode(data);
                    let Data=JSON.parse(string)
                    if(Data.type==="TRANSCRIPTION"){
                      this.props.AddLocalTranscription(Data)
                    }

                    if(Data.type==="GUEST_LOOK_AT_TO_HOST"){
                      if(document.getElementById(peer.peerID+"_VIDEOSPHERE"))
                      // document.getElementById(peer.peerID+"_VIDEOSPHERE").setAttribute("animation","property: position; to: "+Data.position+"; loop: false; dur: 200")
                      document.getElementById(peer.peerID+"_VIDEOSPHERE").setAttribute("position",Data.position)
                    }


          })

        // First add the peer to the state
        console.log('@@ Dispatching ADD_PEER for userId (via receive-call):', from);
        dispatch({ type: "ADD_PEER", peer: peer });

        // Then set the AV config with a small delay to ensure peer is properly added
        setTimeout(() => {
          console.log('@@ Setting AV config for userId (via receive-call):', from, {
            video,
            audio,
            screen
          });

          // Check state before setting AV config
          const stateBeforeAVConfig = getState();
          console.log('@@ State before SET_AV_CONFIG dispatch (via receive-call):', {
            userId: from,
            currentUserVideoAudio: stateBeforeAVConfig.Call.userVideoAudio,
            aboutToSetConfig: { video, audio, screen }
          });

          dispatch({type:"SET_AV_CONFIG",userName:from,config:{ video, audio,screen }})

          // Check state immediately after setting AV config (reduced logging)
          setTimeout(() => {
            const stateAfterAVConfig = getState();
            if (!stateAfterAVConfig.Call.userVideoAudio[from]) {
              console.log('@@ ERROR: State after SET_AV_CONFIG dispatch (via receive-call) - config missing for userId:', from);
            }
          }, 10);

          // Additional delay to ensure state is fully synchronized for new users
          setTimeout(() => {
            console.log('@@ Peer state synchronized for (via receive-call):', from);
            console.log('@@ Final state check - all peers after synchronization (via receive-call):', peersRef.map(p => ({
              peerID: p.peerID,
              userName: p.userName,
              streamVideoEnabled: p.peer?.streams?.[0]?.getVideoTracks()?.[0]?.enabled,
              streamAudioEnabled: p.peer?.streams?.[0]?.getAudioTracks()?.[0]?.enabled
            })));

            // Final Redux state check
            const finalState = getState();
            console.log('@@ Final Redux userVideoAudio state (via receive-call):', finalState.Call.userVideoAudio);
          }, 200);
        }, 100);

        let index = 5;
        setInterval(() => {
          if(index){
            const localVideo = document.getElementById("LocalVideo");
            if (localVideo) {
              localVideo.srcObject = stream;
            }
            index--;
          }
        }, 5000);



      }
    });
    socket.on('FE-toggle-camera', ({ userId, switchTarget,value }) => {
      console.log('@@ FE-toggle-camera received:', { userId, switchTarget, value });
      const peerIdx = findPeer(userId);
      if(peerIdx) {
        console.log('@@ Before toggle - peer stream state:', {
          peerID: peerIdx.peerID,
          streamVideoEnabled: peerIdx.peer?.streams?.[0]?.getVideoTracks()?.[0]?.enabled,
          streamAudioEnabled: peerIdx.peer?.streams?.[0]?.getAudioTracks()?.[0]?.enabled
        });
        console.log('@@ Dispatching SET_AV_INIT:', { peerID: peerIdx.peerID, Target: switchTarget, value });
        dispatch({type:"SET_AV_INIT",peerID:peerIdx.peerID,Target:switchTarget,value:value})
        
        // Log state after dispatch
        setTimeout(() => {
          console.log('@@ After toggle - peer stream state:', {
            peerID: peerIdx.peerID,
            streamVideoEnabled: peerIdx.peer?.streams?.[0]?.getVideoTracks()?.[0]?.enabled,
            streamAudioEnabled: peerIdx.peer?.streams?.[0]?.getAudioTracks()?.[0]?.enabled
          });
        }, 50);
      } else {
        console.log('@@ Peer not found for userId:', userId);
      }
   });
   socket.on('FE-call-accepted', ({ signal, answerId }) => {
     const peerIdx = findPeer(answerId);
     peerIdx.peer.signal(signal);
   });

   // Listen for current state sync requests
   socket.on('FE-sync-current-states', ({ states }) => {
     console.log('@@ FE-sync-current-states received:', states);

     // Check current state before syncing
     const currentState = getState();
     console.log('@@ Current userVideoAudio before sync:', currentState.Call.userVideoAudio);

     // Update each peer's state based on their current actual state
     states.forEach(({ userId, video, audio, screen }) => {
       console.log('@@ Syncing current state for userId:', userId, { video, audio, screen });
       console.log('@@ Before sync - existing config for userId:', currentState.Call.userVideoAudio[userId]);
       dispatch({type:"SET_AV_CONFIG",userName:userId,config:{ video, audio, screen }});
     });

     // Check state after all syncs
     setTimeout(() => {
       const stateAfterSync = getState();
       console.log('@@ userVideoAudio after sync:', stateAfterSync.Call.userVideoAudio);
     }, 50);
   });

   // Listen for requests to broadcast current state (when new users join)
   socket.on('FE-broadcast-current-state', ({ requesterId }) => {
    console.log('@@ FE-broadcast-current-state request from:', requesterId);

    // Get current local state from Redux store
    const state = getState();
    const currentState = {
      video: state.Call.Video,
      audio: state.Call.Audio,
      screen: state.Call.ScreenShare || false
    };

    console.log('@@ Broadcasting current local state for requesterId:', requesterId, currentState);
    console.log('@@ Current full userVideoAudio state when broadcasting:', state.Call.userVideoAudio);

    // Send current state to server
    socket.emit('BE-current-state-response', {
      roomId: window.location.pathname.split('/').pop(),
      userId: socket.id,
      requesterId: requesterId,
      currentState: currentState
    });
  });

  }

}
export function AddLocalTranscription(Data){
  return dispatch=>{
    dispatch({type:"ADD_TRANSCRIPTION",payload:Data})
    if(Data.isFinal){

      setTimeout(() => {
    dispatch({type:"REMOVE_TRANSCRIPTION",payload:Data})

  },2000);

    }

  }

}
export function SubscribeToChat(OnMessage){
  socket.on('FE-receive-message', ({ msg, sender }) => {
    var data=JSON.parse(msg);
    if(data.actiontype==="chat"){
OnMessage(msg)
setTimeout(() => {
  var element = document.getElementById("chat_bar");
  if(element)
  element.scrollTop = element.scrollHeight;
}, 500);
      }
    });
}
export function SubscribeToCustom(OnMessage){
  return dispatch=>{
    socket.on('FE-receive-custom', ({ msg, sender }) => {
      window.log("Received :"+JSON.stringify(msg,null,2));

    OnMessage(msg)

    });
  }

}
export function SetScreenShare(value){
  return dispatch=>{
    dispatch({type:"SETSCREENSHARE",value:value})
  }
}
export function SetClientData(data){
  return dispatch=>{
    dispatch({type:"SET_CLIENT_DATA",payload:data})
  }
}

export function SetLastUpdate(LastUpdate){
  return dispatch=>{
    dispatch({type:"SET_LAST_UPDATE",LastUpdate:LastUpdate})
  }
}

export function ToggleUserVideo(Config){
  return dispatch=>{
    if(Config.Video){
      // If screen sharing is active, clean up local display stream and update state
      if(Config.Screen){
        if (localDisplayStream) {
          localDisplayStream.getTracks().forEach(track => track.stop());
          localDisplayStream = null;

          // Reset local video elements to show canvas (blank)
          updateLocalVideoElements(new MediaStream([canvastrack]));
        }

        // Clear stored camera track
        if (currentVideoTrack) {
          currentVideoTrack.stop();
          currentVideoTrack = null;
        }

        // IMPORTANT: Do NOT modify LocalStream here
        // LocalStream should keep the screen track so guests see screen content
        console.log('@@ Video toggled OFF during screen sharing - LocalStream unchanged, local display cleared');

        dispatch({type:"TOGGLE_LOCAL_AV",MediaType:"Video",value:false})
        dispatch({type:"SET_AV_CONFIG",userName:"localUser",config:{ video: false, audio: Config.Audio, screen: Config.Screen }})
        socket.emit('BE-toggle-camera-audio', { roomId:Config.roomId, switchTarget: "Video",value:false });
        return;
      }

      replaceVideoTrack(Config.Peers, Config.LocalStream, canvastrack);
    dispatch({type:"TOGGLE_LOCAL_AV",MediaType:"Video",value:false})
    dispatch({type:"SET_AV_CONFIG",userName:"localUser",config:{ video: false, audio: Config.Audio, screen: Config.Screen }})
    socket.emit('BE-toggle-camera-audio', { roomId:Config.roomId, switchTarget: "Video",value:false });
    console.log('@@ Video toggled OFF - broadcasting state change');

  }
  else{
    // If screen sharing is active, create separate local display stream for video
    // but keep the screen track in LocalStream for guests
    if(Config.Screen){
      GetUserVideoMedia(true).then(Videotrack=>{
        if (Videotrack.stream && Videotrack.track) {
          // Create local display stream for the host's video card only
          createLocalDisplayStream(Videotrack.track);

          // Store the camera track for later use when screen sharing stops
          currentVideoTrack = Videotrack.track;

          // IMPORTANT: Do NOT modify LocalStream here
          // LocalStream should keep the screen track so guests see screen content
          console.log('@@ Video toggled ON during screen sharing - LocalStream unchanged, local display updated');

          dispatch({type:"TOGGLE_LOCAL_AV",MediaType:"Video",value:true})
          dispatch({type:"SET_AV_CONFIG",userName:"localUser",config:{ video: true, audio: Config.Audio, screen: Config.Screen }})
          socket.emit('BE-toggle-camera-audio', { roomId:Config.roomId, switchTarget: "Video",value:true });
        } else {
          if ((Videotrack.error.toString()).includes("denied")) {
            dispatch({type:"SET_MODAL_EXCEPTION",message:"Camera Access is not allowed"})
          } else if ((Videotrack.error.toString()).includes("device")) {
            dispatch({type:"SET_MODAL_EXCEPTION",message:"Can't find any Camera attached to browser"})
          }
        }
      });
      return;
    }

    GetUserVideoMedia(true).then(Videotrack=>{
      if (Videotrack.stream && Videotrack.track) {
        replaceVideoTrack(Config.Peers, Config.LocalStream, Videotrack.track);
      dispatch({type:"TOGGLE_LOCAL_AV",MediaType:"Video",value:true})
      dispatch({type:"SET_AV_CONFIG",userName:"localUser",config:{ video: true, audio: Config.Audio, screen: Config.Screen }})
      socket.emit('BE-toggle-camera-audio', { roomId:Config.roomId, switchTarget: "Video",value:true });
      console.log('@@ Video toggled ON - broadcasting state change');

    } else {
        if ((Videotrack.error.toString()).includes("denied")) {
          dispatch({type:"SET_MODAL_EXCEPTION",message:"Camera Access is not allowed"})
        } else if ((Videotrack.error.toString()).includes("device")) {
          dispatch({type:"SET_MODAL_EXCEPTION",message:"Can't find any Camera attached to browser"})
        }
       }
    })
  }
}



}


export function ToggleUserAudio(Config){
  return dispatch=>{
    const userAudioTrack = Config.LocalStream.getAudioTracks()[0];
          if(Config.Audio){
            userAudioTrack.enabled = false;
            dispatch({type:"TOGGLE_LOCAL_AV",MediaType:"Audio",value:false})
            dispatch({type:"SET_AV_CONFIG",userName:"localUser",config:{  video: Config.Video, audio: false,screen: Config.Screen }})
            socket.emit('BE-toggle-camera-audio', { roomId:Config.roomId, switchTarget: "Audio",value:false });
            console.log('@@ Audio toggled OFF - broadcasting state change');
          }else{
            userAudioTrack.enabled = true;
            dispatch({type:"TOGGLE_LOCAL_AV",MediaType:"Audio",value:true})
            dispatch({type:"SET_AV_CONFIG",userName:"localUser",config:{  video: Config.Video, audio: true,screen: Config.Screen }})
            socket.emit('BE-toggle-camera-audio', { roomId:Config.roomId, switchTarget: "Audio",value:true });
            console.log('@@ Audio toggled ON - broadcasting state change');
          }
  }
}

// Function to broadcast current state to a specific user or all users
export function BroadcastCurrentState(roomId, targetUserId = null) {
  return (dispatch, getState) => {
    const state = getState();
    const currentState = {
      video: state.Call.Video,
      audio: state.Call.Audio,
      screen: state.Call.ScreenShare || false
    };
    
    console.log('@@ Broadcasting current state:', currentState, 'to:', targetUserId || 'all users');
    
    socket.emit('BE-broadcast-current-state', {
      roomId: roomId,
      userId: socket.id,
      currentState: currentState,
      targetUserId: targetUserId
    });
  };
}
export function ToggleScreenVideo(Config){
  return dispatch=>{

    const SetTrack=(track)=>{
      console.log('SetTrack called with track:', track);
      console.log('Track readyState:', track.readyState);
      console.log('Track enabled:', track.enabled);
      replaceVideoTrack(Config.Peers, Config.LocalStream, track);
    }

    if(!Config.Screen){
      // Starting screen share - store current video track if video is on
      if(Config.Video){
        currentVideoTrack = Config.LocalStream.getVideoTracks()[0];
      }

      navigator.mediaDevices
          .getDisplayMedia({ cursor: true })
          .then((stream) => {
            const screenTrack = stream.getTracks()[0];

            console.log('Screen sharing started - New screen track:', {
              track: screenTrack,
              readyState: screenTrack.readyState,
              enabled: screenTrack.enabled,
              kind: screenTrack.kind
            });

            // Ensure the screen track is enabled and live
            if (screenTrack.readyState === 'live' && screenTrack.enabled) {
              SetTrack(screenTrack);
            } else {
              console.error('Screen track is not live or enabled:', {
                readyState: screenTrack.readyState,
                enabled: screenTrack.enabled
              });
              dispatch({type:"SET_MODAL_EXCEPTION",message:"Screen sharing failed. Please try again."});
              return;
            }

            // If video is on, create local display stream for host's camera
            if(Config.Video){
              if(currentVideoTrack && currentVideoTrack !== canvastrack && currentVideoTrack.readyState === 'live'){
                // Use the stored camera track for local display
                createLocalDisplayStream(currentVideoTrack);
              } else {
                // Get a new camera track for local display
                GetUserVideoMedia(true).then(Videotrack=>{
                  if (Videotrack.stream && Videotrack.track) {
                    currentVideoTrack = Videotrack.track;
                    createLocalDisplayStream(Videotrack.track);
                  }
                });
              }
            } else {
              // If video is off, show canvas in local display
              updateLocalVideoElements(new MediaStream([canvastrack]));
            }

            // Listen click end
            screenTrack.onended = () => {
              console.log('Screen sharing ended automatically');

              // First, clean up all screen sharing references
              cleanupScreenSharing();

              // Screen sharing ended - restore appropriate track based on current video state
              if(Config.Video){
                // If video was on, get a fresh camera track
                console.log('Getting fresh camera track on auto-end');
                GetUserVideoMedia(true).then(Videotrack=>{
                  if (Videotrack.stream && Videotrack.track) {
                    console.log('Fresh camera track obtained on auto-end:', Videotrack.track);
                    SetTrack(Videotrack.track);

                    // Restore normal local video display
                    setTimeout(() => {
                      updateLocalVideoElements(Config.LocalStream);
                    }, 100);
                     }
                  else{
                    console.log('Failed to get camera on auto-end, using canvas');
                    SetTrack(canvastrack);

                    // Restore normal local video display
                    setTimeout(() => {
                      updateLocalVideoElements(Config.LocalStream);
                    }, 100);
                  }
                 })
              }
              else{
                console.log('Video is off on auto-end, using canvas track');
                SetTrack(canvastrack);

                // Restore normal local video display
                setTimeout(() => {
                  updateLocalVideoElements(Config.LocalStream);
                }, 100);
              }

              dispatch({type:"SETSCREENSHARE",value:false})
              dispatch({type:"SET_AV_CONFIG",userName:"localUser",config:{ video: Config.Video, audio: Config.Audio, screen: false }})

             socket.emit('BE-toggle-camera-audio', { roomId:Config.roomId, switchTarget: "screen",value:false });
            };

            socket.emit('BE-toggle-camera-audio', { roomId:Config.roomId, switchTarget: "screen",value:true });

            screenTrackRef = screenTrack;
            dispatch({type:"SETSCREENSHARE",value:true})
            dispatch({type:"SET_AV_CONFIG",userName:"localUser",config:{ video: Config.Video, audio: Config.Audio, screen: true }})
          })
          .catch((error) => {
            console.error('Error starting screen share:', error);
            dispatch({type:"SET_MODAL_EXCEPTION",message:"Screen sharing failed. Please try again."})
          });
    }
    else{
      // Stopping screen share - restore appropriate track based on current video state
      console.log('Stopping screen share manually');

      // First, clean up all screen sharing references
      cleanupScreenSharing();

      if(Config.Video){
        // If video was on, get a fresh camera track
        console.log('Getting fresh camera track for restoration');
        GetUserVideoMedia(true).then(Videotrack=>{
          if (Videotrack.stream && Videotrack.track) {
            console.log('Fresh camera track obtained:', Videotrack.track);
            SetTrack(Videotrack.track);

            // Restore normal local video display
            setTimeout(() => {
              updateLocalVideoElements(Config.LocalStream);
            }, 100);
           }
          else{
            console.log('Failed to get camera, using canvas');
            SetTrack(canvastrack);

            // Restore normal local video display
            setTimeout(() => {
              updateLocalVideoElements(Config.LocalStream);
            }, 100);
          }
         })
      }
      else{
        console.log('Video is off, using canvas track');
        SetTrack(canvastrack);

        // Restore normal local video display
        setTimeout(() => {
          updateLocalVideoElements(Config.LocalStream);
        }, 100);
      }

      dispatch({type:"SETSCREENSHARE",value:false})
      dispatch({type:"SET_AV_CONFIG",userName:"localUser",config:{ video: Config.Video, audio: Config.Audio, screen: false }})

     socket.emit('BE-toggle-camera-audio', { roomId:Config.roomId, switchTarget: "screen",value:false });
    }
  }
}


export function SetScreenShareMinimized(value){
  return dispatch=>{
    dispatch({type:"SET_SCREENSHARE_MINIMIZED",value:value})
  }
}

export function TabControl(value){
  return dispatch=>{
    dispatch({type:"SET_TAB",value:value})
    window.dispatchEvent(new Event('resize'));
    if(value=="CHAT")
    {setTimeout(() => {
      var element = document.getElementById("chat_bar");
      if(element)
      element.scrollTop = element.scrollHeight;
    }, 500);}
  }
}

export function ToggleChat(value){
  return dispatch=>{
    dispatch({type:"TOGGLE_CHAT",value:value})
    window.dispatchEvent(new Event('resize'));
    if(value === true || (value !== false && !value))
    {setTimeout(() => {
      var element = document.getElementById("chat_bar");
      if(element)
      element.scrollTop = element.scrollHeight;
    }, 500);}
  }
}

export function ToggleMembers(value){
  return dispatch=>{
    dispatch({type:"TOGGLE_MEMBERS",value:value})
    window.dispatchEvent(new Event('resize'));
//     setTimeout(() => {
//       var width=window.innerWidth-320;
//       var height=document.getElementById("Propvr_Embed").clientHeight;
//       $('#room').css({'position':'absolute','height':height+"px",'width':width+"px"});
//  // $('#room').attr('embedded',false);
//       let aScene = document.querySelector('a-scene');

//       // Inside the code that fires on resize (you should be able to use the 'onresize' event listener)
//       aScene.camera.aspect =width/height;
//       aScene.camera.updateProjectionMatrix();
//     }, 1000);
setTimeout(() => {
  if(document.querySelector('a-scene')){
    let aScene = document.querySelector('a-scene');

    // Inside the code that fires on resize (you should be able to use the 'onresize' event listener)
    aScene.camera.aspect = aScene.clientWidth/aScene.clientHeight;
    aScene.camera.updateProjectionMatrix();
  }

}, 300);
  }
}


export function findPeer(id) {
  return peersRef.find((p) => p.peerID === id);
}
export function SendMessage(data,roomId) {
  var msg =JSON.stringify({...data,user:socket.id});
      socket.emit('BE-send-message', { roomId:roomId, msg, sender: socket.id });
return dispatch=>{

}
  }
export function SendCustomMessage(data,roomId) {
    var msg =JSON.stringify({...data,user:socket.id});
    window.log("Sent :"+JSON.stringify({ roomId:roomId, msg, sender: socket.id },null,2));

        socket.emit('BE-send-custom', { roomId:roomId, msg, sender: socket.id });
  return dispatch=>{

  }
    }
function createPeer(userId, caller, stream) {
    const peer = new Peer({
      initiator: true,
      trickle: false,
      stream,
      config: IceServers
    });

    peer.on('signal', (signal) => {
      socket.emit('BE-call-user', {
        userToCall: userId,
        from: caller,
        signal,
      });
    });
    peer.on('disconnect', () => {
      peer.reconnect();
    });

    return peer;
  }

  function addPeer(incomingSignal, callerId, stream) {
    const peer = new Peer({
      initiator: false,
      trickle: false,
      stream,
      config: IceServers
    });

    peer.on('signal', (signal) => {
      socket.emit('BE-accept-call', { signal, to: callerId });
    });

    peer.on('disconnect', () => {
      peer.destroy();
    });

    peer.signal(incomingSignal);

    return peer;
  }

  export async function CombineStream(stream){
    return new Promise ((resolve,reject)=>{
      const videoTrack = stream.getVideoTracks()[0];
      if(stream.getAudioTracks()>0){
        var _tempStream= new MediaStream([stream.getAudioTracks()[0]])
      MUX.createMediaStreamSource(_tempStream).connect(MUXDEST);
      }

              const mixedTracks = MUXDEST.stream.getTracks()[0];
              const finalstream = new MediaStream([videoTrack, mixedTracks]);
              resolve(finalstream);
    })
  }
export function SetUnreadMessage(count){
  return dispatch=>{
    dispatch({type:"SET_UNREAD_MESSAGE",count:count})
  }
}
export function GetUserVideoMedia(constraint){
  return new Promise((resolve,reject)=>{
    if(constraint){
    navigator.mediaDevices.getUserMedia({video:constraint}).then(stream=>{
resolve({track:stream.getVideoTracks()[0],stream:stream,error:false})
    }).catch(err=>{
    resolve({track:false,stream:false,error:err})
    })
  }else{
    resolve({track:false,stream:false,error:false})
  }
})}

export function GetUserAudioMedia(constraint){
  return new Promise((resolve,reject)=>{
    if(constraint){
    navigator.mediaDevices.getUserMedia({audio:constraint}).then(stream=>{
resolve({track:stream.getAudioTracks()[0],stream:stream,error:false})
    }).catch(err=>{
    resolve({track:false,stream:false,error:err})
    })
  }else{
    resolve({track:false,stream:false,error:false})
  }
})}

var endTime;
var startTime;
  var testConnectionSpeed = {
    imageAddr : "https://upload.wikimedia.org/wikipedia/commons/a/a6/Brandenburger_Tor_abends.jpg", // this is just an example, you rather want an image hosted on your server
    downloadSize : 2707459, // this must match with the image above
    run:function(mbps_max,cb_gt,cb_lt){
      testConnectionSpeed.mbps_max = parseFloat(mbps_max) ? parseFloat(mbps_max) : 0;
      testConnectionSpeed.cb_gt = cb_gt;
      testConnectionSpeed.cb_lt = cb_lt;
      testConnectionSpeed.InitiateSpeedDetection();
    },
    InitiateSpeedDetection: function() {

      window.setTimeout(testConnectionSpeed.MeasureConnectionSpeed, 1);
    },
    result:function(){
      var duration = (endTime - startTime) / 1000;
      var bitsLoaded = testConnectionSpeed.downloadSize * 8;
      var speedBps = (bitsLoaded / duration).toFixed(2);
      var speedKbps = (speedBps / 1024).toFixed(2);
      var speedMbps = (speedKbps / 1024).toFixed(2);
      testConnectionSpeed.cb_gt(speedMbps)

      // if(speedMbps >= (testConnectionSpeed.max_mbps ? testConnectionSpeed.max_mbps : 1) ){
      //   testConnectionSpeed.cb_gt ? testConnectionSpeed.cb_gt(speedMbps) : false;
      // }else {
      //   testConnectionSpeed.cb_lt ? testConnectionSpeed.cb_lt(speedMbps) : false;
      // }
    },
    MeasureConnectionSpeed:function() {
      var download = new Image();
      download.onload = function () {
          endTime = (new Date()).getTime();
          testConnectionSpeed.result();
      }
      startTime = (new Date()).getTime();
      var cacheBuster = "?nnn=" + startTime;
      download.src = testConnectionSpeed.imageAddr + cacheBuster;
    }
  }
 function getProjectsRecords(Projects) {
    return new Promise((resolve, reject) => {

      Projects.child("screen").listAll().then(Files => {
        if (Files.items.length) {

          Promise.all([...Files.items].map(getMetadata)).then(response => {
            resolve(response.reduce(function (a, b) { return a + b; }, 0))

          }
          )

        }
        else {
          resolve(0)
        }

      })

    })
  }
 export function getRooms(folderref) {
    return new Promise((resolve, reject) => {

      folderref.child("rooms").listAll().then((Rooms) => {
        Promise.all([...Rooms.prefixes].map(getProjectsRecords)).then(meta => {
          resolve(meta.reduce(function (a, b) { return a + b; }, 0))
        })

      })
    })
  }
 function getMetadata(File) {

    return new Promise((resolve, reject) => {
      File.getMetadata().then(Metadata => {


        resolve(Metadata.size)

      })


    })
  }

// Function to unsubscribe from chat events
export function UnsubscribeFromChat() {
  if (socket) {
    socket.off('FE-receive-message');
  }
}