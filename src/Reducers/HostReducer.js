var c = document.createElement("canvas");
var ctx = c.getContext("2d");
ctx.beginPath();
ctx.rect(20, 20, 150, 100);
ctx.fillStyle = "red";
ctx.fill();
ctx.beginPath();
ctx.rect(40, 40, 150, 100);
ctx.fillStyle = "blue";
ctx.fill();
const canvasstreamg = c.captureStream(1)
export const canvastrack = canvasstreamg.getVideoTracks()[0]
canvastrack.enabled = true;
const AudioContext = window.AudioContext || window.webkitAudioContext;
let Audioctx = new AudioContext(), oscillator = Audioctx.createOscillator();
let dst = oscillator.connect(Audioctx.createMediaStreamDestination());
oscillator.type = 'square';
oscillator.frequency.setValueAtTime(0, Audioctx.currentTime);
oscillator.start();
let DummyAudio = dst.stream.getAudioTracks()[0];
DummyAudio.enabled = false;
const INITIAL_STATE = {
    peers: {},
    VideoDevices: [],
    SocketId: false,
    LocalStream: false,
    SETUP: false,
    UserName: "default",
    userVideoAudio: {
        localUser: { video: true, audio: true }
    },
    peersRef: [],
    config:false,
    Audio: false,
    Video: false,

    userscount:0,
    HostStatus:false,
    HostCtrl:false,
    CanvasTrack: canvastrack,
    DummyAudio: DummyAudio,


    LastUpdate:false,
    ClientData:false,
    Messages: [],
    Transcription:{},
    UnreadCount:0,
    Tab:false, // Keeping for backward compatibility
    ChatOpen: false,
    MembersOpen: false,
    ShowControls:true,
    ScreenShare: false,
    ScreenShareMinimized: false,

    RecordingStatus: false,
    RecordingCloudSize: null,
    Recorder: false,
    NetWorkSpeed: false,

    HasCamera: false,
    HasMicrophone: false
}

export function CallHandle(state = INITIAL_STATE, action) {
    switch (action.type) {
        case "ADD_PEER":
            return { ...state, peers: {...state.peers,[action.peer.peerID]:action.peer} ,userscount:state.userscount+1}
        case "UPDATE_PEERS":
            return { ...state, peers: {...state.peers,[action.peerid]:null} ,userscount:state.userscount==0?0:state.userscount-1}
        case "SET_VIDEO_DEVICES":
            return { ...state, VideoDevices: action.Devices }
        case "CONNECTED_GOOD_TO_GO":
            return { ...state, SocketId: action.socketid, SETUP: true, UserName: action.userName }
        case "ADD_LOCAL_STREAM":
            return { ...state, LocalStream: action.LocalStream }
        case "UPDATE_CONFIG":
            console.log("UPDATE_CONFIG")
            return { ...state, userVideoAudio: { ...state.userVideoAudio, [action.peer]: action.config } }
        case "SET_AV_CONFIG":
            console.log('@@ SET_AV_CONFIG reducer:', action.userName, action.config);
            return { ...state, userVideoAudio: { ...state.userVideoAudio, [action.userName]: action.config } }
        case "SET_AV_INIT":
            console.log('SET_AV_INIT reducer - Before:', {
              peerID: action.peerID,
              Target: action.Target,
              value: action.value,
              currentState: state.userVideoAudio[action.peerID]
            });

            // Ensure the peer exists in userVideoAudio state, initialize with defaults if not
            const currentPeerState = state.userVideoAudio[action.peerID] || { video: false, audio: false, screen: false };

            let video = currentPeerState.video;
            let audio = currentPeerState.audio;
            let screen = currentPeerState.screen;
            if (action.Target === 'Video') video = action.value;
            else if (action.Target === "Audio") audio = action.value;
            else if (action.Target === "screen") screen = action.value;
            const newState = { ...state, userVideoAudio: { ...state.userVideoAudio, [action.peerID]: { video, audio, screen } } };
            console.log('SET_AV_INIT reducer - After:', {
              peerID: action.peerID,
              newPeerState: newState.userVideoAudio[action.peerID],
              allUserVideoAudio: newState.userVideoAudio
            });
            return newState;
        case "TOGGLE_LOCAL_AV":
            return { ...state, [action.MediaType]: action.value }
        case "START_RECORDING":
            return { ...state, Recorder: true }
        case "STOP_RECORD":
            return { ...state, Recorder: false }
        case "SET_MESSAGE":
            return { ...state, Messages: [...state.Messages, action.data] }
        case "SETSCREENSHARE":
            return { ...state, ScreenShare: action.value }
        case "SET_SCREENSHARE_MINIMIZED":
            return { ...state, ScreenShareMinimized: action.value }
        case "SET_RECORD_QUOTA":
            return { ...state, RecordingCloudSize: action.size }
        case "NETWORK_SPEED":
            return { ...state, NetWorkSpeed: action.payload }
        case "SET_HAS_CAMERA":
            return { ...state, HasCamera: action.value }
        case "SET_HAS_MICROPHONE":
            return { ...state, HasMicrophone: action.value }
        case "SET_CONFIG":
            console.log("SET_CONFIG")
            return {...state,config:action.config}
        case "SET_CLIENT_DATA":
            return {...state,ClientData:action.payload}
        case "SET_LAST_UPDATE":
            return {...state,LastUpdate:action.LastUpdate}
        case "SET_TAB":
            // For backward compatibility
            if(action.value=="CHAT"){
                return {...state, UnreadCount:0, Tab:action.value, ChatOpen: true, MembersOpen: false}
            }
            else if(action.value=="MEMBERS"){
                return {...state, Tab:action.value, MembersOpen: true, ChatOpen: false}
            }
            else{
                return {...state, Tab:action.value, ChatOpen: false, MembersOpen: false}
            }
        case "TOGGLE_CHAT":
            if(action.value === true) {
                return {...state, UnreadCount:0, ChatOpen: true, Tab: "CHAT"}
            } else if(action.value === false) {
                return {...state, ChatOpen: false, Tab: state.MembersOpen ? "MEMBERS" : false}
            } else {
                return {...state, UnreadCount: action.value ? 0 : state.UnreadCount, ChatOpen: !state.ChatOpen, Tab: !state.ChatOpen ? "CHAT" : (state.MembersOpen ? "MEMBERS" : false)}
            }
        case "TOGGLE_MEMBERS":
            if(action.value === true) {
                return {...state, MembersOpen: true, Tab: "MEMBERS"}
            } else if(action.value === false) {
                return {...state, MembersOpen: false, Tab: state.ChatOpen ? "CHAT" : false}
            } else {
                return {...state, MembersOpen: !state.MembersOpen, Tab: !state.MembersOpen ? "MEMBERS" : (state.ChatOpen ? "CHAT" : false)}
            }
        case "INVERT_CONTROLS":
            console.log('INVERT_CONTROLS reducer called, current state:', state.ShowControls);
            return {...state, ShowControls: !state.ShowControls}
        case "TOGGLE_CONTROLS":
            console.log('TOGGLE_CONTROLS reducer called with value:', action.value);
            return {...state, ShowControls: action.value}
        case "SET_UNREAD_MESSAGE":
            return {...state,UnreadCount:action.count}
        case  "ADD_TRANSCRIPTION":
            return {...state,Transcription:{...state.Transcription,[action.payload.time]:action.payload}}
        case "REMOVE_TRANSCRIPTION":
            var Data=delete state.Transcription[action.payload.time]
            return {...state,Transcription:Data}
        case "SET_HOST_STATUS":
            return {...state,HostStatus:action.status}
        default:
            return state;
    }
}

